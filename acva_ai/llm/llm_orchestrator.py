import asyncio
import logging
from typing import Dict, List, Optional, Tuple, Enum

from acva_ai.llm.llm_providers.azure_call import AzureProvider
from acva_ai.llm.llm_providers.lazarus_call import LazarusProvider
from acva_ai.llm.llm_providers.openai_call import OpenAIProvider
from acva_ai.utils.usage import ResponseUsage

logger = logging.getLogger(__name__)


class LLMProvider(str, Enum):
    """Enum for supported LLM providers."""

    AZURE = AzureProvider
    OPENAI = OpenAIProvider
    LAZARUS = LazarusProvider


class LLMOrchestrator:
    """
    Orchestrates calls to different LLM providers with fallback capabilities.
    """

    def __init__(
        self,
        primary_provider: LLMProvider,
        max_retries: int = 5,
        retry_delay: float = 10,
    ):
        """
        Initialize the LLM orchestrator.

        Args:
            primary_provider: The primary LLM provider to use (LLMProvider enum)
            max_retries: Maximum number of retries per provider
            retry_delay: Initial delay before retrying (will increase exponentially)
        """
        self.primary_provider = primary_provider
        self.fallback_providers = self._get_default_fallbacks(self.primary_provider)
        self.max_retries = max_retries
        self.retry_delay = retry_delay

        # Initialize provider instances
        self.providers = {
            LLMProvider.AZURE: AzureProvider(),
            LLMProvider.OPENAI: OpenAIProvider(),
            LLMProvider.LAZARUS: LazarusProvider(),
        }

    def _get_default_fallbacks(self, primary: LLMProvider) -> List[LLMProvider]:
        """
        Get default fallback providers based on the primary provider.

        Args:
            primary: The primary provider

        Returns:
            List of fallback providers
        """
        all_providers = [LLMProvider.LAZARUS, LLMProvider.OPENAI, LLMProvider.AZURE]
        return [p for p in all_providers if p != primary]

    async def _call_provider(
        self,
        provider: LLMProvider,
        prompt: str,
        max_tokens: int,
        use_cache: bool,
        response_usage: Optional[ResponseUsage],
        temperature: float,
    ) -> Tuple[bool, Optional[Dict], Optional[Exception]]:
        """
        Call a specific LLM provider with error handling.

        Args:
            provider: The LLM provider to use
            prompt: The prompt to send
            model_id: The model ID to use
            max_tokens: Maximum tokens to generate
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            temperature: Temperature parameter for the model

        Returns:
            Tuple of (success, response, error)
        """
        try:
            # Get the provider instance
            provider_instance = self.providers.get(provider)
            if provider_instance is None:
                return False, None, ValueError(f"Unsupported provider: {provider}")

            # Call the provider's call_llm method
            response = await provider_instance.call_llm(
                prompt=prompt,
                max_tokens=max_tokens,
                use_cache=use_cache,
                response_usage=response_usage,
                max_retries=self.max_retries,
                retry_delay=self.retry_delay,
                temperature=temperature,
            )

            return True, response, None

        except Exception as e:
            logger.error(f"Error calling {provider.value} provider: {str(e)}")
            return False, None, e

    async def call_llm(
        self,
        prompt: str,
        max_tokens: int = 5000,
        use_cache: bool = False,
        response_usage: Optional[ResponseUsage] = None,
        temperature: float = 0,
    ) -> Dict:
        """
        Call an LLM with fallback capabilities.

        Args:
            prompt: The prompt to send
            max_tokens: Maximum tokens to generate
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            temperature: Temperature parameter for the model

        Returns:
            The model's response as a dictionary

        Raises:
            Exception: If all providers fail
        """
        current_provider = self.primary_provider

        # Try the primary provider first
        success, response, error = await self._call_provider(
            provider=current_provider,
            prompt=prompt,
            max_tokens=max_tokens,
            use_cache=use_cache,
            response_usage=response_usage,
            temperature=temperature,
        )

        if success:
            return response

        # If primary provider fails, try fallbacks
        logger.warning(
            f"Primary provider {self.primary_provider} failed with error: {error}. Trying fallbacks."
        )

        for fallback_provider in self.fallback_providers:
            logger.info(f"Trying fallback provider: {fallback_provider}")

            success, response, error = await self._call_provider(
                provider=fallback_provider,
                prompt=prompt,
                max_tokens=max_tokens,
                use_cache=use_cache,
                response_usage=response_usage,
                temperature=temperature,
            )

            if success:
                logger.info(f"Successfully used fallback provider: {fallback_provider}")
                return response

            logger.warning(
                f"Fallback provider {fallback_provider} also failed: {error}"
            )

        # If we get here, all providers failed
        error_msg = f"All LLM providers failed. Last error: {error}"
        logger.error(error_msg)
        raise Exception(error_msg)

    def _get_default_model_id(self, provider: LLMProvider) -> str:
        """
        Get the default model ID for a provider.

        Args:
            provider: The LLM provider

        Returns:
            Default model ID for the provider
        """
        match provider:
            case LLMProvider.AZURE:
                from acva_ai._params import OPENAI_MODEL_ID

                return OPENAI_MODEL_ID

            case LLMProvider.OPENAI:
                from acva_ai._params import OPENAI_MODEL_ID

                return OPENAI_MODEL_ID

            case LLMProvider.LAZARUS:
                from acva_ai._params import LAZARUS_MODEL_ID

                return LAZARUS_MODEL_ID

            case _:
                raise ValueError(f"Unsupported provider: {provider}")


# TODO Codrin: How about move this to a special directory of test files? This way we can try checking the performance of the different providers.
async def test():
    """Test the LLM orchestrator."""
    response_usage = ResponseUsage()

    # Create orchestrator with OpenAI as primary
    orchestrator = LLMOrchestrator(
        primary_provider=LLMProvider.OPENAI,
    )

    # Test with a simple prompt
    result = await orchestrator.call_llm(
        prompt="What is the capital of France?",
        use_cache=False,
        response_usage=response_usage,
    )
    print("Result from orchestrator:")
    print(result)
    print(f"Usage: {response_usage}")

    # Test with explicit provider selection
    result_lazarus = await orchestrator.call_llm(
        prompt="What is the capital of Italy?",
        use_cache=False,
        response_usage=response_usage,
    )
    print("\nResult from explicit Lazarus provider:")
    print(result_lazarus)
    print(f"Usage: {response_usage}")

    # Test with message list for OpenAI
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "What is the capital of Germany?"},
    ]
    result_messages = await orchestrator.call_llm(
        prompt=messages,
        use_cache=False,
        response_usage=response_usage,
    )
    print("\nResult from OpenAI with message list:")
    print(result_messages)
    print(f"Usage: {response_usage}")


if __name__ == "__main__":
    asyncio.run(test())
